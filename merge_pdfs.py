from PyPDF2 import PdfMerger
import os

def merge_pdfs(pdf_list, output_file):
    merger = PdfMerger()
    for pdf in pdf_list:
        if os.path.exists(pdf):
            merger.append(pdf)
        else:
            print(f"⚠️ Skipping: {pdf} (file not found)")
    merger.write(output_file)
    merger.close()
    print(f"✅ Merged PDF saved as: {output_file}")

if __name__ == "__main__":
    # List your PDF files here in the order you want them merged
    pdf_files = ["Preface.pdf", "file2.pdf", "file3.pdf"]

    merge_pdfs(pdf_files, "merged_output.pdf")
